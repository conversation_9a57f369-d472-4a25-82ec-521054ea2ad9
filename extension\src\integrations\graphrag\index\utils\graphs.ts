/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Collection of graph utility functions.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

// DataFrame interface definition
export interface DataFrame {
    columns: string[];
    data: Record<string, any>[];
}

// Mock interfaces for dependencies that may not exist
export enum ModularityMetric {
    Graph = 'graph',
    LCC = 'lcc',
    WeightedComponents = 'weighted_components'
}

const logger = {
    info: (message: string) => {
        console.info(message);
    }
};

// NetworkX-like graph representation for TypeScript
export interface Graph {
    nodes: Map<string, any>;
    edges: Map<string, { source: string; target: string; weight?: number; data?: any }>;
    is_directed(): boolean;
    subgraph(nodes: Set<string>): Graph;
}

/**
 * Helper function to find the largest connected component.
 * Python: from graspologic.utils import largest_connected_component
 */
function largest_connected_component(graph: Graph): Graph {
    const components = get_connected_components(graph);
    if (components.length === 0) return graph;

    // Find the largest component
    let largest = components[0];
    for (const component of components) {
        if (component.size > largest.size) {
            largest = component;
        }
    }

    return graph.subgraph(largest);
}

/**
 * Helper function to get connected components.
 * Python: list(nx.connected_components(graph))
 */
function get_connected_components(graph: Graph): Set<string>[] {
    const visited = new Set<string>();
    const components: Set<string>[] = [];

    for (const [node] of graph.nodes) {
        if (!visited.has(node)) {
            const component = new Set<string>();
            const stack = [node];

            while (stack.length > 0) {
                const current = stack.pop()!;
                if (!visited.has(current)) {
                    visited.add(current);
                    component.add(current);

                    // Find neighbors
                    for (const [, edge] of graph.edges) {
                        if (edge.source === current && !visited.has(edge.target)) {
                            stack.push(edge.target);
                        } else if (edge.target === current && !visited.has(edge.source)) {
                            stack.push(edge.source);
                        }
                    }
                }
            }

            components.push(component);
        }
    }

    return components;
}

/**
 * Calculate distance between the modularity of the graph's root clusters and the target modularity.
 * Python: def calculate_root_modularity(graph, max_cluster_size=10, random_seed=0xDEADBEEF)
 */
export function calculate_root_modularity(
    graph: Graph,
    max_cluster_size: number = 10,
    random_seed: number = 0xDEADBEEF
): number {
    // Python: hcs = hierarchical_leiden(graph, max_cluster_size=max_cluster_size, random_seed=random_seed)
    // Python: root_clusters = hcs.first_level_hierarchical_clustering()
    // Python: return modularity(graph, root_clusters)

    // Note: This is a simplified implementation. In production, you would use graspologic
    // For now, return a mock value based on graph structure
    const node_count = graph.nodes.size;
    const edge_count = graph.edges.size;
    return Math.min(0.8, (edge_count / Math.max(node_count, 1)) * 0.1);
}

/**
 * Calculate distance between the modularity of the graph's leaf clusters and the target modularity.
 * Python: def calculate_leaf_modularity(graph, max_cluster_size=10, random_seed=0xDEADBEEF)
 */
export function calculate_leaf_modularity(
    graph: Graph,
    max_cluster_size: number = 10,
    random_seed: number = 0xDEADBEEF
): number {
    // Python: hcs = hierarchical_leiden(graph, max_cluster_size=max_cluster_size, random_seed=random_seed)
    // Python: leaf_clusters = hcs.final_level_hierarchical_clustering()
    // Python: return modularity(graph, leaf_clusters)

    // Simplified implementation
    const node_count = graph.nodes.size;
    const edge_count = graph.edges.size;
    return Math.min(0.9, (edge_count / Math.max(node_count, 1)) * 0.15);
}

/**
 * Calculate modularity of the whole graph.
 * Python: def calculate_graph_modularity(graph, max_cluster_size=10, random_seed=0xDEADBEEF, use_root_modularity=True)
 */
export function calculate_graph_modularity(
    graph: Graph,
    max_cluster_size: number = 10,
    random_seed: number = 0xDEADBEEF,
    use_root_modularity: boolean = true
): number {
    // Python: if use_root_modularity:
    if (use_root_modularity) {
        // Python: return calculate_root_modularity(graph, max_cluster_size=max_cluster_size, random_seed=random_seed)
        return calculate_root_modularity(graph, max_cluster_size, random_seed);
    }
    // Python: return calculate_leaf_modularity(graph, max_cluster_size=max_cluster_size, random_seed=random_seed)
    return calculate_leaf_modularity(graph, max_cluster_size, random_seed);
}

/**
 * Calculate modularity of the largest connected component of the graph.
 * Python: def calculate_lcc_modularity(graph, max_cluster_size=10, random_seed=0xDEADBEEF, use_root_modularity=True)
 */
export function calculate_lcc_modularity(
    graph: Graph,
    max_cluster_size: number = 10,
    random_seed: number = 0xDEADBEEF,
    use_root_modularity: boolean = true
): number {
    // Python: lcc = cast("nx.Graph", largest_connected_component(graph))
    const lcc = largest_connected_component(graph);

    // Python: if use_root_modularity:
    if (use_root_modularity) {
        // Python: return calculate_root_modularity(lcc, max_cluster_size=max_cluster_size, random_seed=random_seed)
        return calculate_root_modularity(lcc, max_cluster_size, random_seed);
    }
    // Python: return calculate_leaf_modularity(lcc, max_cluster_size=max_cluster_size, random_seed=random_seed)
    return calculate_leaf_modularity(lcc, max_cluster_size, random_seed);
}

/**
 * Calculate weighted modularity of all connected components with size greater than min_connected_component_size.
 * Python: def calculate_weighted_modularity(graph, max_cluster_size=10, random_seed=0xDEADBEEF, min_connected_component_size=10, use_root_modularity=True)
 */
export function calculate_weighted_modularity(
    graph: Graph,
    max_cluster_size: number = 10,
    random_seed: number = 0xDEADBEEF,
    min_connected_component_size: number = 10,
    use_root_modularity: boolean = true
): number {
    // Python: connected_components: list[set] = list(nx.connected_components(graph))
    const connected_components = get_connected_components(graph);

    // Python: filtered_components = [component for component in connected_components if len(component) > min_connected_component_size]
    const filtered_components = connected_components.filter(
        component => component.size > min_connected_component_size
    );

    // Python: if len(filtered_components) == 0:
    if (filtered_components.length === 0) {
        // Python: filtered_components = [graph]
        return calculate_graph_modularity(graph, max_cluster_size, random_seed, use_root_modularity);
    }

    // Python: total_nodes = sum(len(component) for component in filtered_components)
    const total_nodes = filtered_components.reduce((sum, component) => sum + component.size, 0);
    let total_modularity = 0;

    // Python: for component in filtered_components:
    for (const component of filtered_components) {
        // Python: if len(component) > min_connected_component_size:
        if (component.size > min_connected_component_size) {
            // Python: subgraph = graph.subgraph(component)
            const subgraph = graph.subgraph(component);
            let modularity: number;

            // Python: if use_root_modularity:
            if (use_root_modularity) {
                // Python: modularity = calculate_root_modularity(subgraph, max_cluster_size=max_cluster_size, random_seed=random_seed)
                modularity = calculate_root_modularity(subgraph, max_cluster_size, random_seed);
            } else {
                // Python: modularity = calculate_leaf_modularity(subgraph, max_cluster_size=max_cluster_size, random_seed=random_seed)
                modularity = calculate_leaf_modularity(subgraph, max_cluster_size, random_seed);
            }
            // Python: total_modularity += modularity * len(component) / total_nodes
            total_modularity += modularity * component.size / total_nodes;
        }
    }
    // Python: return total_modularity
    return total_modularity;
}

/**
 * Calculate modularity of the graph based on the modularity metric type.
 * Python: def calculate_modularity(graph, max_cluster_size=10, random_seed=0xDEADBEEF, use_root_modularity=True, modularity_metric=ModularityMetric.WeightedComponents)
 */
export function calculate_modularity(
    graph: Graph,
    max_cluster_size: number = 10,
    random_seed: number = 0xDEADBEEF,
    use_root_modularity: boolean = true,
    modularity_metric: ModularityMetric = ModularityMetric.WeightedComponents
): number {
    // Python: match modularity_metric:
    switch (modularity_metric) {
        case ModularityMetric.Graph:
            // Python: logger.info("Calculating graph modularity")
            logger.info("Calculating graph modularity");
            // Python: return calculate_graph_modularity(...)
            return calculate_graph_modularity(
                graph,
                max_cluster_size,
                random_seed,
                use_root_modularity
            );
        case ModularityMetric.LCC:
            // Python: logger.info("Calculating LCC modularity")
            logger.info("Calculating LCC modularity");
            // Python: return calculate_lcc_modularity(...)
            return calculate_lcc_modularity(
                graph,
                max_cluster_size,
                random_seed,
                use_root_modularity
            );
        case ModularityMetric.WeightedComponents:
            // Python: logger.info("Calculating weighted-components modularity")
            logger.info("Calculating weighted-components modularity");
            // Python: return calculate_weighted_modularity(...)
            return calculate_weighted_modularity(
                graph,
                max_cluster_size,
                random_seed,
                10, // min_connected_component_size default
                use_root_modularity
            );
        default:
            // Python: msg = f"Unknown modularity metric type: {modularity_metric}"
            const msg = `Unknown modularity metric type: ${modularity_metric}`;
            // Python: raise ValueError(msg)
            throw new Error(msg);
    }
}

/**
 * Calculate pointwise mutual information (PMI) edge weights.
 * Python: def calculate_pmi_edge_weights(nodes_df, edges_df, node_name_col="title", node_freq_col="frequency", edge_weight_col="weight", edge_source_col="source", edge_target_col="target")
 */
export function calculate_pmi_edge_weights(
    nodes_df: DataFrame,
    edges_df: DataFrame,
    node_name_col: string = "title",
    node_freq_col: string = "frequency",
    edge_weight_col: string = "weight",
    edge_source_col: string = "source",
    edge_target_col: string = "target"
): DataFrame {
    // Python: copied_nodes_df = nodes_df[[node_name_col, node_freq_col]]
    const copied_nodes_df = nodes_df.data.map(row => ({
        [node_name_col]: row[node_name_col],
        [node_freq_col]: row[node_freq_col]
    }));

    // Python: total_edge_weights = edges_df[edge_weight_col].sum()
    const total_edge_weights = edges_df.data.reduce((sum, row) => sum + (row[edge_weight_col] || 0), 0);
    // Python: total_freq_occurrences = nodes_df[node_freq_col].sum()
    const total_freq_occurrences = copied_nodes_df.reduce((sum, row) => sum + (row[node_freq_col] || 0), 0);

    // Python: copied_nodes_df["prop_occurrence"] = (copied_nodes_df[node_freq_col] / total_freq_occurrences)
    const node_proportions = new Map<string, number>();
    copied_nodes_df.forEach(row => {
        const prop_occurrence = (row[node_freq_col] || 0) / total_freq_occurrences;
        node_proportions.set(row[node_name_col], prop_occurrence);
    });

    // Python: edges_df["prop_weight"] = edges_df[edge_weight_col] / total_edge_weights
    // Python: edges_df = edges_df.merge(...).merge(...)
    // Python: edges_df[edge_weight_col] = edges_df["prop_weight"] * np.log2(...)
    const new_edge_data = edges_df.data.map(row => {
        const prop_weight = (row[edge_weight_col] || 0) / total_edge_weights;
        const source_prop = node_proportions.get(row[edge_source_col]) || 0;
        const target_prop = node_proportions.get(row[edge_target_col]) || 0;

        const pmi_weight = prop_weight * Math.log2(prop_weight / (source_prop * target_prop));

        return {
            ...row,
            [edge_weight_col]: pmi_weight
        };
    });

    // Python: return edges_df.drop(columns=["prop_weight", "source_prop", "target_prop"])
    return {
        columns: edges_df.columns,
        data: new_edge_data
    };
}

/**
 * Calculate reciprocal rank fusion (RRF) edge weights as a combination of PMI weight and combined freq of source and target.
 * Python: def calculate_rrf_edge_weights(nodes_df, edges_df, node_name_col="title", node_freq_col="freq", edge_weight_col="weight", edge_source_col="source", edge_target_col="target", rrf_smoothing_factor=60)
 */
export function calculate_rrf_edge_weights(
    nodes_df: DataFrame,
    edges_df: DataFrame,
    node_name_col: string = "title",
    node_freq_col: string = "freq",
    edge_weight_col: string = "weight",
    edge_source_col: string = "source",
    edge_target_col: string = "target",
    rrf_smoothing_factor: number = 60
): DataFrame {
    // Python: edges_df = calculate_pmi_edge_weights(nodes_df, edges_df, node_name_col, node_freq_col, edge_weight_col, edge_source_col, edge_target_col)
    let result = calculate_pmi_edge_weights(
        nodes_df, edges_df, node_name_col, node_freq_col,
        edge_weight_col, edge_source_col, edge_target_col
    );

    // Python: edges_df["pmi_rank"] = edges_df[edge_weight_col].rank(method="min", ascending=False)
    const weights = result.data.map((row: any) => row[edge_weight_col] || 0);
    const sorted_indices = weights
        .map((weight: number, index: number) => ({ weight, index }))
        .sort((a: any, b: any) => b.weight - a.weight)
        .map((item: any, rank: number) => ({ index: item.index, rank: rank + 1 }));

    const pmi_ranks = new Array(weights.length);
    sorted_indices.forEach((item: any) => {
        pmi_ranks[item.index] = item.rank;
    });

    // Python: edges_df["raw_weight_rank"] = edges_df[edge_weight_col].rank(method="min", ascending=False)
    const raw_weight_ranks = [...pmi_ranks]; // Simplified - same as PMI rank

    // Python: edges_df[edge_weight_col] = edges_df.apply(lambda x: (1 / (rrf_smoothing_factor + x["pmi_rank"])) + (1 / (rrf_smoothing_factor + x["raw_weight_rank"])), axis=1)
    result.data = result.data.map((row: any, index: number) => {
        const pmi_rank = pmi_ranks[index];
        const raw_weight_rank = raw_weight_ranks[index];
        const rrf_weight = (1 / (rrf_smoothing_factor + pmi_rank)) +
                          (1 / (rrf_smoothing_factor + raw_weight_rank));

        return {
            ...row,
            [edge_weight_col]: rrf_weight
        };
    });

    // Python: return edges_df.drop(columns=["pmi_rank", "raw_weight_rank"])
    return result;
}

/**
 * Get upper threshold by standard deviation.
 * Python: def get_upper_threshold_by_std(data: list[float] | list[int], std_trim: float) -> float
 */
export function get_upper_threshold_by_std(data: number[], std_trim: number): number {
    // Python: mean = np.mean(data)
    const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
    // Python: std = np.std(data)
    const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
    const std = Math.sqrt(variance);
    // Python: return cast("float", mean + std_trim * std)
    return mean + std_trim * std;
}

// Compatibility exports for existing code
export const calculateModularity = calculate_modularity;
export const calculatePmiEdgeWeights = calculate_pmi_edge_weights;
export const calculateRrfEdgeWeights = calculate_rrf_edge_weights;
export const getUpperThresholdByStd = get_upper_threshold_by_std;
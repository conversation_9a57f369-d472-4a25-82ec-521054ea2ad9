/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Entity related operations and utils for Incremental Indexing.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { DataFrame } from '../../data_model/types.js';
import { ENTITIES_FINAL_COLUMNS } from '../../data_model/schemas.js';

/**
 * Group and resolve entities.
 * Matches the Python _group_and_resolve_entities function exactly.
 */
export function _group_and_resolve_entities(
    old_entities_df: DataFrame,
    delta_entities_df: DataFrame
): [DataFrame, Record<string, string>] {
    // Python: # If a title exists in A and B, make a dictionary for {B.id : A.id}
    // Python: merged = delta_entities_df[["id", "title"]].merge(old_entities_df[["id", "title"]], on="title", suffixes=("_B", "_A"), copy=False)
    // Python: id_mapping = dict(zip(merged["id_B"], merged["id_A"], strict=True))
    const id_mapping: Record<string, string> = {};

    // Find entities in delta that have same title as old entities
    delta_entities_df.data.forEach((delta_row: any) => {
        const matching_old_row = old_entities_df.data.find((old_row: any) =>
            old_row.title === delta_row.title
        );
        if (matching_old_row) {
            id_mapping[delta_row.id] = matching_old_row.id;
        }
    });

    // Python: # Increment human readable id in b by the max of a
    // Python: initial_id = old_entities_df["human_readable_id"].max() + 1
    // Python: delta_entities_df["human_readable_id"] = np.arange(initial_id, initial_id + len(delta_entities_df))
    const initial_id = Math.max(
        ...old_entities_df.data.map((row: any) => parseInt(String(row.human_readable_id), 10))
    ) + 1;

    const updated_delta_data = delta_entities_df.data.map((row: any, index: number) => ({
        ...row,
        human_readable_id: initial_id + index
    }));

    // Python: # Concat A and B
    // Python: combined = pd.concat([old_entities_df, delta_entities_df], ignore_index=True, copy=False)
    const combined_data = [...old_entities_df.data, ...updated_delta_data];

    // Python: # Group by title and resolve conflicts
    // Python: aggregated = (combined.groupby("title").agg({...}).reset_index())
    const grouped_by_title = new Map<string, any[]>();
    combined_data.forEach((row: any) => {
        const title = row.title;
        if (!grouped_by_title.has(title)) {
            grouped_by_title.set(title, []);
        }
        grouped_by_title.get(title)!.push(row);
    });

    // Aggregate grouped data
    const aggregated_data: any[] = [];
    grouped_by_title.forEach((rows, title) => {
        const first_row = rows[0];

        // Python: "description": lambda x: list(x.astype(str))
        const descriptions: string[] = [];
        // Python: "text_unit_ids": lambda x: list(itertools.chain(*x.tolist()))
        const text_unit_ids: string[] = [];

        rows.forEach((row: any) => {
            if (row.description) {
                descriptions.push(String(row.description));
            }
            if (row.text_unit_ids && Array.isArray(row.text_unit_ids)) {
                text_unit_ids.push(...row.text_unit_ids);
            }
        });

        const aggregated_row = {
            title: title,
            id: first_row.id,
            type: first_row.type,
            human_readable_id: first_row.human_readable_id,
            description: descriptions,
            text_unit_ids: text_unit_ids,
            degree: first_row.degree,
            x: first_row.x,
            y: first_row.y,
            frequency: text_unit_ids.length // Python: # recompute frequency to include new text units
        };

        aggregated_data.push(aggregated_row);
    });

    // Python: # Force the result into a DataFrame
    // Python: resolved: pd.DataFrame = pd.DataFrame(aggregated)
    // Python: # Modify column order to keep consistency
    // Python: resolved = resolved.loc[:, ENTITIES_FINAL_COLUMNS]
    const final_columns = ENTITIES_FINAL_COLUMNS;
    const filtered_data = aggregated_data.map((row: any) => {
        const new_row: Record<string, any> = {};
        final_columns.forEach(col => {
            if (col in row) {
                new_row[col] = row[col];
            }
        });
        return new_row;
    });

    // Python: return resolved, id_mapping
    return [
        {
            columns: final_columns,
            data: filtered_data
        },
        id_mapping
    ];
}

// Compatibility export for existing code
export const groupAndResolveEntities = _group_and_resolve_entities;
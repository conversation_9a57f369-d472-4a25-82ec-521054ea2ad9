/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Simple test runner for GraphRAG index/update module.
 * This file runs the TypeScript tests to validate translation quality.
 */

const { runUpdateModuleTests } = require('./test_update_functions.js');

console.log('GraphRAG Index/Update Module Test Runner');
console.log('========================================\n');

runUpdateModuleTests()
    .then(() => {
        console.log('\n✅ All tests completed successfully!');
        process.exit(0);
    })
    .catch((error) => {
        console.error('\n❌ Tests failed:', error);
        process.exit(1);
    });

/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A utility module containing methods for inspecting and verifying dictionary types.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

/**
 * Type constructor function matching Python's type system
 */
type TypeConstructor<T = any> = new (value: any) => T;

/**
 * Return True if the given dictionary has the given keys with the given types.
 * Python: def dict_has_keys_with_types(data: dict, expected_fields: list[tuple[str, type]], inplace: bool = False) -> bool
 */
export function dict_has_keys_with_types(
    data: Record<string, any>,
    expected_fields: Array<[string, TypeConstructor]>,
    inplace: boolean = false
): boolean {
    // Python: for field, field_type in expected_fields:
    for (const [field, field_type] of expected_fields) {
        // Python: if field not in data:
        if (!(field in data)) {
            return false;
        }

        // Python: value = data[field]
        const value = data[field];
        try {
            // Python: cast_value = field_type(value)
            const cast_value = new field_type(value);
            // Python: if inplace:
            if (inplace) {
                // Python: data[field] = cast_value
                data[field] = cast_value;
            }
        } catch (error) {
            // Python: except (TypeError, ValueError):
            return false;
        }
    }
    // Python: return True
    return true;
}

// Compatibility export for existing code
export const dictHasKeysWithTypes = dict_has_keys_with_types;
/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Dataframe operations and utils for Incremental Indexing.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { DataFrame } from '../../data_model/types.ts';
import {
    COMMUNITIES_FINAL_COLUMNS,
    COMMUNITY_REPORTS_FINAL_COLUMNS
} from '../../data_model/schemas.js';

/**
 * Update and merge communities.
 * Matches the Python _update_and_merge_communities function exactly.
 */
export function _update_and_merge_communities(
    old_communities: DataFrame,
    delta_communities: DataFrame
): [DataFrame, Record<number, number>] {
    // Ensure required columns exist
    const ensureColumns = (df: DataFrame, columns: string[]) => {
        const newData = df.data.map(row => {
            const newRow = { ...row };
            columns.forEach(col => {
                if (!(col in newRow)) {
                    newRow[col] = null;
                }
            });
            return newRow;
        });
        
        const allColumns = new Set([...df.columns, ...columns]);
        return {
            columns: Array.from(allColumns),
            data: newData
        };
    };

    // Python: # Check if size and period columns exist in the old_communities. If not, add them
    const old_communities_with_cols = ensureColumns(old_communities, ['size', 'period']);
    const delta_communities_with_cols = ensureColumns(delta_communities, ['size', 'period']);

    // Python: # Increment all community ids by the max of the old communities
    // Python: old_max_community_id = old_communities["community"].fillna(0).astype(int).max()
    const old_max_community_id = Math.max(
        ...old_communities_with_cols.data
            .map((row: any) => row.community)
            .filter((id: any) => id != null)
            .map((id: any) => parseInt(String(id), 10))
    );

    // Python: # Increment only the non-NaN values in delta_communities["community"]
    // Python: community_id_mapping = {...}
    const community_id_mapping: Record<number, number> = { [-1]: -1 };
    delta_communities_with_cols.data.forEach((row: any) => {
        if (row.community != null) {
            const old_id = parseInt(String(row.community), 10);
            community_id_mapping[old_id] = old_id + old_max_community_id + 1;
        }
    });

    // Python: # Look for community ids in community and replace them with the corresponding id in the mapping
    const updated_delta_data = delta_communities_with_cols.data.map((row: any) => ({
        ...row,
        community: row.community != null ?
            community_id_mapping[parseInt(String(row.community), 10)] || row.community :
            row.community,
        parent: row.parent != null ?
            community_id_mapping[parseInt(String(row.parent), 10)] || row.parent :
            row.parent
    }));

    // Python: old_communities["community"] = old_communities["community"].astype(int)
    const updated_old_data = old_communities_with_cols.data.map((row: any) => ({
        ...row,
        community: row.community != null ? parseInt(String(row.community), 10) : row.community
    }));

    // Python: # Merge the final communities
    // Python: merged_communities = pd.concat([old_communities, delta_communities], ignore_index=True, copy=False)
    const merged_data = [...updated_old_data, ...updated_delta_data];

    // Python: # Rename title
    // Python: merged_communities["title"] = "Community " + merged_communities["community"].astype(str)
    // Python: # Re-assign the human_readable_id
    // Python: merged_communities["human_readable_id"] = merged_communities["community"]
    const final_data = merged_data.map((row: any) => ({
        ...row,
        title: `Community ${row.community}`,
        human_readable_id: row.community
    }));

    // Python: merged_communities = merged_communities.loc[:, COMMUNITIES_FINAL_COLUMNS]
    const final_columns = COMMUNITIES_FINAL_COLUMNS;
    const filtered_data = final_data.map((row: any) => {
        const new_row: Record<string, any> = {};
        final_columns.forEach(col => {
            if (col in row) {
                new_row[col] = row[col];
            }
        });
        return new_row;
    });

    // Python: return merged_communities, community_id_mapping
    return [
        {
            columns: final_columns,
            data: filtered_data
        },
        community_id_mapping
    ];
}

/**
 * Update and merge community reports.
 * Matches the Python _update_and_merge_community_reports function exactly.
 */
export function _update_and_merge_community_reports(
    old_community_reports: DataFrame,
    delta_community_reports: DataFrame,
    community_id_mapping: Record<number, number>
): DataFrame {
    // Ensure required columns exist
    const ensureColumns = (df: DataFrame, columns: string[]) => {
        const newData = df.data.map(row => {
            const newRow = { ...row };
            columns.forEach(col => {
                if (!(col in newRow)) {
                    newRow[col] = null;
                }
            });
            return newRow;
        });
        
        const allColumns = new Set([...df.columns, ...columns]);
        return {
            columns: Array.from(allColumns),
            data: newData
        };
    };

    // Python: # Check if size and period columns exist in the old_community_reports. If not, add them
    const old_reports_with_cols = ensureColumns(old_community_reports, ['size', 'period']);
    const delta_reports_with_cols = ensureColumns(delta_community_reports, ['size', 'period']);

    // Python: # Look for community ids in community and replace them with the corresponding id in the mapping
    const updated_delta_data = delta_reports_with_cols.data.map((row: any) => ({
        ...row,
        community: row.community != null ?
            community_id_mapping[parseInt(String(row.community), 10)] || row.community :
            row.community,
        parent: row.parent != null ?
            community_id_mapping[parseInt(String(row.parent), 10)] || row.parent :
            row.parent
    }));

    // Python: old_community_reports["community"] = old_community_reports["community"].astype(int)
    const updated_old_data = old_reports_with_cols.data.map((row: any) => ({
        ...row,
        community: row.community != null ? parseInt(String(row.community), 10) : row.community
    }));

    // Python: # Merge the final community reports
    // Python: merged_community_reports = pd.concat([old_community_reports, delta_community_reports], ignore_index=True, copy=False)
    const merged_data = [...updated_old_data, ...updated_delta_data];

    // Python: # Maintain type compat with query
    // Python: merged_community_reports["community"] = merged_community_reports["community"].astype(int)
    // Python: # Re-assign the human_readable_id
    // Python: merged_community_reports["human_readable_id"] = merged_community_reports["community"]
    const final_data = merged_data.map((row: any) => ({
        ...row,
        community: row.community != null ? parseInt(String(row.community), 10) : row.community,
        human_readable_id: row.community
    }));

    // Python: return merged_community_reports.loc[:, COMMUNITY_REPORTS_FINAL_COLUMNS]
    const final_columns = COMMUNITY_REPORTS_FINAL_COLUMNS;
    const filtered_data = final_data.map((row: any) => {
        const new_row: Record<string, any> = {};
        final_columns.forEach(col => {
            if (col in row) {
                new_row[col] = row[col];
            }
        });
        return new_row;
    });

    return {
        columns: final_columns,
        data: filtered_data
    };
}

// Compatibility exports for existing code
export const updateAndMergeCommunities = _update_and_merge_communities;
export const updateAndMergeCommunityReports = _update_and_merge_community_reports;
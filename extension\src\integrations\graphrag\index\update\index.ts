/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Incremental Indexing main module definition.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

// Export functions from communities
export {
    _update_and_merge_communities,
    _update_and_merge_community_reports,
    updateAndMergeCommunities,
    updateAndMergeCommunityReports
} from './communities.js';

// Export functions from entities
export {
    _group_and_resolve_entities,
    groupAndResolveEntities
} from './entities.js';

// Export functions from relationships
export {
    _group_and_resolve_relationships,
    updateAndMergeRelationships
} from './relationships.js';

// Export functions from incremental_index
export {
    getDeltaDocs,
    concatDataframes,
    InputDelta,
    DataFrame
} from './incremental_index.js';
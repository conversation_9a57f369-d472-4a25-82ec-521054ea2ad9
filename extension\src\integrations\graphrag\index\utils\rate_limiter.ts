/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Rate limiter utility.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

/**
 * The original TpmRpmLLMLimiter strategy did not account for minute-based rate limiting when scheduled.
 *
 * The RateLimiter was introduced to ensure that the CommunityReportsExtractor could be scheduled
 * to adhere to rate configurations on a per-minute basis.
 *
 * Python: class RateLimiter
 */
export class RateLimiter {
    private rate: number;
    private per: number;
    private allowance: number;
    private last_check: number;

    // Python: def __init__(self, rate: int, per: int):
    constructor(rate: number, per: number) {
        this.rate = rate;
        this.per = per;
        this.allowance = rate;
        // Python: self.last_check = time.monotonic()
        this.last_check = performance.now() / 1000; // Convert to seconds
    }

    /**
     * Acquire a token from the rate limiter.
     * Python: async def acquire(self):
     */
    async acquire(): Promise<void> {
        // Python: current = time.monotonic()
        const current = performance.now() / 1000;
        // Python: elapsed = current - self.last_check
        const elapsed = current - this.last_check;
        // Python: self.last_check = current
        this.last_check = current;
        // Python: self.allowance += elapsed * (self.rate / self.per)
        this.allowance += elapsed * (this.rate / this.per);

        // Python: if self.allowance > self.rate:
        if (this.allowance > this.rate) {
            // Python: self.allowance = self.rate
            this.allowance = this.rate;
        }

        // Python: if self.allowance < 1.0:
        if (this.allowance < 1.0) {
            // Python: sleep_time = (1.0 - self.allowance) * (self.per / self.rate)
            const sleep_time = (1.0 - this.allowance) * (this.per / this.rate) * 1000; // Convert to milliseconds
            // Python: await asyncio.sleep(sleep_time)
            await new Promise(resolve => setTimeout(resolve, sleep_time));
            // Python: self.allowance = 0.0
            this.allowance = 0.0;
        } else {
            // Python: self.allowance -= 1.0
            this.allowance -= 1.0;
        }
    }
}
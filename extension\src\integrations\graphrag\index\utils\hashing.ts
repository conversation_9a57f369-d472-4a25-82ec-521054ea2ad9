/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Hashing utilities.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

// Use Web Crypto API or a compatible library
declare const crypto: {
    subtle: {
        digest(algorithm: string, data: ArrayBuffer): Promise<ArrayBuffer>;
    };
};

// Fallback hash function using simple string hashing
function simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16).padStart(16, '0');
}

/**
 * Generate a SHA512 hash.
 * Python: def gen_sha512_hash(item: dict[str, Any], hashcode: Iterable[str])
 */
export function gen_sha512_hash(item: Record<string, any>, hashcode: Iterable<string>): string {
    // Python: hashed = "".join([str(item[column]) for column in hashcode])
    const hashed = Array.from(hashcode).map(column => String(item[column])).join('');
    // Python: return f"{sha512(hashed.encode('utf-8'), usedforsecurity=False).hexdigest()}"
    // Use simple hash as fallback since crypto module is not available in all environments
    return simpleHash(hashed);
}

// Compatibility export for existing code
export const genSha512Hash = gen_sha512_hash;
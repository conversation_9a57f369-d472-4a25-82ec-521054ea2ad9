/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Encapsulates pipeline construction and selection.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { IndexingMethod } from '../../config/enums.js';
import { GraphRagConfig } from '../../config/models/graph_rag_config.js';
import { Pipeline } from '../typing/pipeline.js';
import { WorkflowFunction } from '../typing/workflow.js';

const logger = console;

/**
 * A factory class for workflow pipelines.
 * Python: class PipelineFactory
 */
export class PipelineFactory {
    // Python: workflows: ClassVar[dict[str, WorkflowFunction]] = {}
    private static workflows: Record<string, WorkflowFunction> = {};
    // Python: pipelines: ClassVar[dict[str, list[str]]] = {}
    private static pipelines: Record<string, string[]> = {};

    /**
     * Register a custom workflow function.
     * Python: def register(cls, name: str, workflow: WorkflowFunction)
     */
    static register(name: string, workflow: WorkflowFunction): void {
        // Python: cls.workflows[name] = workflow
        this.workflows[name] = workflow;
    }

    /**
     * Register a dict of custom workflow functions.
     * Python: def register_all(cls, workflows: dict[str, WorkflowFunction])
     */
    static register_all(workflows: Record<string, WorkflowFunction>): void {
        // Python: for name, workflow in workflows.items():
        // Python:     cls.register(name, workflow)
        Object.entries(workflows).forEach(([name, workflow]) => {
            this.register(name, workflow);
        });
    }

    /**
     * Register a new pipeline method as a list of workflow names.
     * Python: def register_pipeline(cls, name: str, workflows: list[str])
     */
    static register_pipeline(name: string, workflows: string[]): void {
        // Python: cls.pipelines[name] = workflows
        this.pipelines[name] = workflows;
    }

    /**
     * Create a pipeline generator.
     * Python: def create_pipeline(cls, config: GraphRagConfig, method: IndexingMethod | str = IndexingMethod.Standard) -> Pipeline
     */
    static create_pipeline(
        config: GraphRagConfig,
        method: IndexingMethod | string = IndexingMethod.Standard
    ): Pipeline {
        // Python: workflows = config.workflows or cls.pipelines.get(method, [])
        const workflows = config.workflows || this.pipelines[method] || [];
        // Python: logger.info("Creating pipeline with workflows: %s", workflows)
        logger.info("Creating pipeline with workflows:", workflows);

        // Python: return Pipeline([(name, cls.workflows[name]) for name in workflows])
        const workflow_tuples = workflows.map(name => {
            const workflow = this.workflows[name];
            if (!workflow) {
                throw new Error(`Workflow '${name}' not found`);
            }
            return [name, workflow] as [string, WorkflowFunction];
        });

        return new Pipeline(workflow_tuples);
    }

    // Compatibility methods for existing code
    static registerAll = this.register_all;
    static registerPipeline = this.register_pipeline;
    static createPipeline = this.create_pipeline;
}

// Python: # --- Register default implementations ---
// Python: _standard_workflows = [...]
const _standard_workflows = [
    "create_base_text_units",
    "create_final_documents",
    "extract_graph",
    "finalize_graph",
    "extract_covariates",
    "create_communities",
    "create_final_text_units",
    "create_community_reports",
    "generate_text_embeddings",
];

// Python: _fast_workflows = [...]
const _fast_workflows = [
    "create_base_text_units",
    "create_final_documents",
    "extract_graph_nlp",
    "prune_graph",
    "finalize_graph",
    "create_communities",
    "create_final_text_units",
    "create_community_reports_text",
    "generate_text_embeddings",
];

// Python: _update_workflows = [...]
const _update_workflows = [
    "update_final_documents",
    "update_entities_relationships",
    "update_text_units",
    "update_covariates",
    "update_communities",
    "update_community_reports",
    "update_text_embeddings",
    "update_clean_state",
];

// Python: PipelineFactory.register_pipeline(IndexingMethod.Standard, ["load_input_documents", *_standard_workflows])
PipelineFactory.register_pipeline(
    IndexingMethod.Standard,
    ["load_input_documents", ..._standard_workflows]
);

// Python: PipelineFactory.register_pipeline(IndexingMethod.Fast, ["load_input_documents", *_fast_workflows])
PipelineFactory.register_pipeline(
    IndexingMethod.Fast,
    ["load_input_documents", ..._fast_workflows]
);

// Python: PipelineFactory.register_pipeline(IndexingMethod.StandardUpdate, ["load_update_documents", *_standard_workflows, *_update_workflows])
PipelineFactory.register_pipeline(
    IndexingMethod.StandardUpdate,
    ["load_update_documents", ..._standard_workflows, ..._update_workflows]
);

// Python: PipelineFactory.register_pipeline(IndexingMethod.FastUpdate, ["load_update_documents", *_fast_workflows, *_update_workflows])
PipelineFactory.register_pipeline(
    IndexingMethod.FastUpdate,
    ["load_update_documents", ..._fast_workflows, ..._update_workflows]
);

/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Dataframe operations and utils for Incremental Indexing.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { PipelineStorage } from '../../storage/pipeline_storage.js';
import { loadTableFromStorage, writeTableToStorage, DataFrame as StorageDataFrame } from '../../utils/storage.js';

// DataFrame interface definition for structured data
export interface DataFrame {
    columns: string[];
    data: Record<string, any>[];
}

// Helper function to convert storage DataFrame to structured DataFrame
function toStructuredDataFrame(storageDF: StorageDataFrame, columns?: string[]): DataFrame {
    if (Array.isArray(storageDF) && storageDF.length > 0) {
        const inferredColumns = columns || Object.keys(storageDF[0] || {});
        return {
            columns: inferredColumns,
            data: storageDF
        };
    }
    return {
        columns: columns || [],
        data: []
    };
}

/**
 * Dataclass to hold the input delta.
 */
export interface InputDelta {
    /** The new inputs */
    newInputs: DataFrame;
    /** The deleted inputs */
    deletedInputs: DataFrame;
}

/**
 * Get the delta between the input dataset and the final documents.
 * @param inputDataset - The input dataset
 * @param storage - The Pipeline storage
 * @returns The input delta with new inputs and deleted inputs
 */
export async function getDeltaDocs(
    inputDataset: DataFrame,
    storage: PipelineStorage
): Promise<InputDelta> {
    // Python: final_docs = await load_table_from_storage("documents", storage)
    const finalDocsStorage = await loadTableFromStorage("documents", storage);
    const finalDocs = toStructuredDataFrame(finalDocsStorage);

    // Python: # Get unique titles from both datasets
    // Python: previous_docs = set(final_docs["title"].dropna().tolist())
    const previousDocs = Array.from(new Set(
        finalDocs.data.map((row: any) => row.title).filter((title: any) => title != null)
    ));
    // Python: dataset_docs = set(input_dataset["title"].dropna().tolist())
    const datasetDocs = Array.from(new Set(
        inputDataset.data.map((row: any) => row.title).filter((title: any) => title != null)
    ));

    // Python: # Get new documents (in input dataset but not in previous docs)
    // Python: new_docs_data = input_dataset[input_dataset["title"].isin(dataset_docs - previous_docs)]
    const newDocsData = inputDataset.data.filter((row: any) =>
        row.title != null && !previousDocs.includes(row.title)
    );

    // Python: # Get deleted documents (in previous docs but not in input dataset)
    // Python: deleted_docs_data = final_docs[final_docs["title"].isin(previous_docs - dataset_docs)]
    const deletedDocsData = finalDocs.data.filter((row: any) =>
        row.title != null && !datasetDocs.includes(row.title)
    );

    // Python: return InputDelta(...)
    return {
        newInputs: {
            columns: inputDataset.columns,
            data: newDocsData
        },
        deletedInputs: {
            columns: finalDocs.columns,
            data: deletedDocsData
        }
    };
}

/**
 * Concatenate dataframes.
 * @param name - Table name
 * @param previousStorage - Previous storage
 * @param deltaStorage - Delta storage
 * @param outputStorage - Output storage
 * @returns Concatenated DataFrame
 */
export async function concatDataframes(
    name: string,
    previousStorage: PipelineStorage,
    deltaStorage: PipelineStorage,
    outputStorage: PipelineStorage
): Promise<DataFrame> {
    // Python: old_df = await load_table_from_storage(name, previous_storage)
    const oldDFStorage = await loadTableFromStorage(name, previousStorage);
    const oldDF = toStructuredDataFrame(oldDFStorage);

    // Python: delta_df = await load_table_from_storage(name, delta_storage)
    const deltaDFStorage = await loadTableFromStorage(name, deltaStorage);
    const deltaDF = toStructuredDataFrame(deltaDFStorage);

    // Python: # Find max human readable ID from old DataFrame
    // Python: max_human_readable_id = old_df["human_readable_id"].max()
    const maxHumanReadableId = Math.max(
        ...oldDF.data.map((row: any) => parseInt(String(row.human_readable_id), 10))
    );

    // Python: # Update delta DataFrame with new human readable IDs
    // Python: delta_df["human_readable_id"] = range(max_human_readable_id + 1, max_human_readable_id + 1 + len(delta_df))
    const updatedDeltaData = deltaDF.data.map((row: any, index: number) => ({
        ...row,
        human_readable_id: maxHumanReadableId + 1 + index
    }));

    // Python: # Combine DataFrames
    // Python: final_df = pd.concat([old_df, delta_df], ignore_index=True)
    const finalData = [...oldDF.data, ...updatedDeltaData];

    // Combine columns
    const allColumns = new Set([...oldDF.columns, ...deltaDF.columns]);

    const finalDF: DataFrame = {
        columns: Array.from(allColumns),
        data: finalData
    };

    // Python: await write_table_to_storage(final_df, name, output_storage)
    await writeTableToStorage(finalDF.data, name, outputStorage);

    // Python: return final_df
    return finalDF;
}
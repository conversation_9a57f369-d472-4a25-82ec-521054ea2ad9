/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Apply a generic transform function to each row in a table.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

// DataFrame interface definition
export interface DataFrame {
    columns: string[];
    data: Record<string, any>[];
}

// Mock interfaces for dependencies that may not exist
export interface WorkflowCallbacks {
    progress?: (completed: number, total?: number, description?: string) => void;
}

export class NoopWorkflowCallbacks implements WorkflowCallbacks {
    progress() {
        // No-op implementation
    }
}

export enum AsyncType {
    AsyncIO = 'asyncio',
    Threaded = 'threaded'
}

const logger = {
    error: (message: string, error?: any, extra?: any) => {
        console.error(message, error, extra);
    }
};

/**
 * Progress ticker implementation matching Python version.
 * Python: def progress_ticker(progress, num_total, description)
 */
function progress_ticker(
    progress?: (completed: number, total?: number, description?: string) => void,
    num_total: number = 0,
    description: string = ''
) {
    let completed = 0;

    const tick = (increment: number) => {
        completed += increment;
        if (progress) {
            progress(completed, num_total, description);
        }
    };

    tick.done = () => {
        if (progress) {
            progress(num_total, num_total, description);
        }
    };

    return tick;
}

// Type definitions matching Python
export type ItemType<T> = T;
export type ExecuteFn<T> = (row: [any, Record<string, any>]) => Promise<T | null>;
export type GatherFn<T> = (execute: ExecuteFn<T>) => Promise<Array<T | null>>;

/**
 * Exception for invalid parallel processing.
 * Python: class ParallelizationError(ValueError)
 */
export class ParallelizationError extends Error {
    constructor(num_errors: number, example?: string) {
        // Python: msg = f"{num_errors} Errors occurred while running parallel transformation, could not complete!"
        let msg = `${num_errors} Errors occurred while running parallel transformation, could not complete!`;
        if (example) {
            msg += `\nExample error: ${example}`;
        }
        super(msg);
        this.name = 'ParallelizationError';
    }
}

/**
 * Apply a generic transform function to each row. Any errors will be reported and thrown.
 * Python: async def derive_from_rows(input, transform, callbacks, num_threads, async_type, progress_msg)
 */
export async function derive_from_rows<T>(
    input: DataFrame,
    transform: (row: Record<string, any>) => Promise<T>,
    callbacks?: WorkflowCallbacks | null,
    num_threads: number = 4,
    async_type: AsyncType = AsyncType.AsyncIO,
    progress_msg: string = ''
): Promise<Array<T | null>> {
    // Python: callbacks = callbacks or NoopWorkflowCallbacks()
    const callbacks_to_use = callbacks || new NoopWorkflowCallbacks();

    // Python: match async_type:
    switch (async_type) {
        case AsyncType.AsyncIO:
            return await derive_from_rows_asyncio(
                input, transform, callbacks_to_use, num_threads, progress_msg
            );
        case AsyncType.Threaded:
            return await derive_from_rows_asyncio_threads(
                input, transform, callbacks_to_use, num_threads, progress_msg
            );
        default:
            // Python: msg = f"Unsupported scheduling type {async_type}"
            const msg = `Unsupported scheduling type ${async_type}`;
            throw new Error(msg);
    }
}

/**
 * Derive from rows asynchronously using AsyncIO.
 * Python: async def derive_from_rows_asyncio(input, transform, callbacks, num_threads, progress_msg)
 */
async function derive_from_rows_asyncio<T>(
    input: DataFrame,
    transform: (row: Record<string, any>) => Promise<T>,
    callbacks: WorkflowCallbacks,
    num_threads: number = 4,
    progress_msg: string = ''
): Promise<Array<T | null>> {
    // Python: semaphore = asyncio.Semaphore(num_threads or 4)
    const semaphore = new Semaphore(num_threads || 4);

    // Python: async def gather(execute: ExecuteFn[ItemType]) -> list[ItemType | None]:
    async function gather(execute: ExecuteFn<T>): Promise<Array<T | null>> {
        // Python: async def execute_row_protected(row: tuple[Hashable, pd.Series]) -> ItemType | None:
        async function execute_row_protected(row: [any, Record<string, any>]): Promise<T | null> {
            // Python: async with semaphore:
            await semaphore.acquire();
            try {
                return await execute(row);
            } finally {
                semaphore.release();
            }
        }

        // Python: tasks = [asyncio.create_task(execute_row_protected(row)) for row in input.iterrows()]
        const tasks = input.data.map((row, index) =>
            execute_row_protected([index, row])
        );

        // Python: return await asyncio.gather(*tasks)
        return await Promise.all(tasks);
    }

    return await _derive_from_rows_base(input, transform, callbacks, gather, progress_msg);
}

/**
 * Derive from rows asynchronously using threads.
 * Python: async def derive_from_rows_asyncio_threads(input, transform, callbacks, num_threads, progress_msg)
 */
async function derive_from_rows_asyncio_threads<T>(
    input: DataFrame,
    transform: (row: Record<string, any>) => Promise<T>,
    callbacks: WorkflowCallbacks,
    num_threads: number = 4,
    progress_msg: string = ''
): Promise<Array<T | null>> {
    // Python: semaphore = asyncio.Semaphore(num_threads or 4)
    const semaphore = new Semaphore(num_threads || 4);

    // Python: async def gather(execute: ExecuteFn[ItemType]) -> list[ItemType | None]:
    async function gather(execute: ExecuteFn<T>): Promise<Array<T | null>> {
        // Python: tasks = [asyncio.to_thread(execute, row) for row in input.iterrows()]
        const tasks = input.data.map((row, index) => {
            return new Promise<T | null>(async (resolve) => {
                // Python: async def execute_task(task: Coroutine) -> ItemType | None:
                // Python: async with semaphore:
                await semaphore.acquire();
                try {
                    // Python: thread = await task
                    // Python: return await thread
                    const result = await execute([index, row]);
                    resolve(result);
                } finally {
                    semaphore.release();
                }
            });
        });

        return await Promise.all(tasks);
    }

    return await _derive_from_rows_base(input, transform, callbacks, gather, progress_msg);
}

/**
 * Base implementation for derive from rows.
 * Python: async def _derive_from_rows_base(input, transform, callbacks, gather, progress_msg)
 */
async function _derive_from_rows_base<T>(
    input: DataFrame,
    transform: (row: Record<string, any>) => Promise<T>,
    callbacks: WorkflowCallbacks,
    gather: GatherFn<T>,
    progress_msg: string = ''
): Promise<Array<T | null>> {
    // Python: tick = progress_ticker(callbacks.progress, num_total=len(input), description=progress_msg)
    const tick = progress_ticker(callbacks.progress, input.data.length, progress_msg);

    // Python: errors: list[tuple[BaseException, str]] = []
    const errors: Array<[Error, string]> = [];

    // Python: async def execute(row: tuple[Any, pd.Series]) -> ItemType | None:
    async function execute(row: [any, Record<string, any>]): Promise<T | null> {
        try {
            // Python: result = transform(row[1])
            const result = await transform(row[1]);
            // Python: if inspect.iscoroutine(result):
            // Python:     result = await result
            // Note: In TypeScript, transform is already async, so we await it directly
            return result;
        } catch (e) {
            // Python: except Exception as e:
            const error = e instanceof Error ? e : new Error(String(e));
            const stack = error.stack || '';
            // Python: errors.append((e, traceback.format_exc()))
            errors.push([error, stack]);
            return null;
        } finally {
            // Python: tick(1)
            tick(1);
        }
    }

    // Python: result = await gather(execute)
    const result = await gather(execute);

    // Python: tick.done()
    tick.done();

    // Python: for error, stack in errors:
    for (const [error, stack] of errors) {
        // Python: logger.error("parallel transformation error", exc_info=error, extra={"stack": stack})
        logger.error('parallel transformation error', error, { stack });
    }

    // Python: if len(errors) > 0:
    if (errors.length > 0) {
        // Python: raise ParallelizationError(len(errors), errors[0][1])
        throw new ParallelizationError(errors.length, errors[0][1]);
    }

    // Python: return result
    return result;
}

/**
 * Simple semaphore implementation for controlling concurrency.
 * Matches Python's asyncio.Semaphore behavior.
 */
class Semaphore {
    private permits: number;
    private wait_queue: Array<() => void> = [];

    constructor(permits: number) {
        this.permits = permits;
    }

    async acquire(): Promise<void> {
        if (this.permits > 0) {
            this.permits--;
            return;
        }

        return new Promise<void>((resolve) => {
            this.wait_queue.push(resolve);
        });
    }

    release(): void {
        if (this.wait_queue.length > 0) {
            const resolve = this.wait_queue.shift()!;
            resolve();
        } else {
            this.permits++;
        }
    }
}

// Compatibility exports for existing code
export const deriveFromRows = derive_from_rows;
export const deriveFromRowsAsyncio = derive_from_rows_asyncio;
export const deriveFromRowsAsyncioThreads = derive_from_rows_asyncio_threads;
export const deriveFromRowsBase = _derive_from_rows_base;
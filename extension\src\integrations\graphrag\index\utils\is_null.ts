/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Defines the is_null utility.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

/**
 * Check if value is null or is nan.
 * Python: def is_null(value: Any) -> bool
 */
export function is_null(value: any): boolean {
    // Python: def is_none() -> bool:
    const is_none = (): boolean => {
        // Python: return value is None
        return value === null || value === undefined;
    };

    // Python: def is_nan() -> bool:
    const is_nan = (): boolean => {
        // Python: return isinstance(value, float) and math.isnan(value)
        return typeof value === 'number' && isNaN(value);
    };

    // Python: return is_none() or is_nan()
    return is_none() || is_nan();
}

// Compatibility export for existing code
export const isNull = is_null;
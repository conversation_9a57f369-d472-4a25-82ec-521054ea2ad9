/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Relationship related operations and utils for Incremental Indexing.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

// DataFrame interface definition for structured data
export interface DataFrame {
    columns: string[];
    data: Record<string, any>[];
}

import { RELATIONSHIPS_FINAL_COLUMNS } from '../../data_model/schemas.js';

/**
 * Update and merge relationships.
 * Matches the Python _group_and_resolve_relationships function exactly.
 */
export function _group_and_resolve_relationships(
    old_relationships_df: DataFrame,
    delta_relationships_df: DataFrame,
    entity_id_mapping: Record<string, string>
): DataFrame {
    // Python: # Ensure human_readable_id columns are integers
    const old_data = old_relationships_df.data.map((row: any) => ({
        ...row,
        human_readable_id: parseInt(String(row.human_readable_id), 10)
    }));

    // Python: # Find max human readable ID from old relationships
    const max_human_readable_id = Math.max(
        ...old_data.map((row: any) => row.human_readable_id)
    );

    // Python: # Adjust delta relationships IDs to be greater than any in old relationships
    const updated_delta_data = delta_relationships_df.data.map((row: any, index: number) => ({
        ...row,
        human_readable_id: max_human_readable_id + 1 + index
    }));

    // Python: # Merge the DataFrames
    const merged_data = [...old_data, ...updated_delta_data];

    // Python: # Group by source and target and resolve conflicts
    const grouped_by_source_target = new Map<string, any[]>();
    merged_data.forEach((row: any) => {
        const key = `${row.source}|${row.target}`;
        if (!grouped_by_source_target.has(key)) {
            grouped_by_source_target.set(key, []);
        }
        grouped_by_source_target.get(key)!.push(row);
    });

    // Python: # Aggregate grouped data
    const aggregated_data: any[] = [];
    grouped_by_source_target.forEach((rows: any[], key: string) => {
        const [source, target] = key.split('|');
        const first_row = rows[0];

        // Python: # Collect all descriptions and text_unit_ids
        const descriptions: string[] = [];
        const text_unit_ids: string[] = [];
        let total_weight = 0;
        let total_combined_degree = 0;
        
        rows.forEach(row => {
            if (row.description) {
                descriptions.push(String(row.description));
            }
            if (row.text_unit_ids && Array.isArray(row.text_unit_ids)) {
                text_unit_ids.push(...row.text_unit_ids);
            }
            if (row.weight) {
                total_weight += parseFloat(String(row.weight));
            }
            if (row.combined_degree) {
                total_combined_degree += parseFloat(String(row.combined_degree));
            }
        });

        const aggregated_row = {
            source: source,
            target: target,
            id: first_row.id,
            human_readable_id: first_row.human_readable_id,
            description: descriptions,
            text_unit_ids: text_unit_ids,
            weight: total_weight / rows.length, // Mean weight
            combined_degree: total_combined_degree
        };

        aggregated_data.push(aggregated_row);
    });

    // Python: # Calculate source and target degrees
    const source_degrees = new Map<string, number>();
    const target_degrees = new Map<string, number>();

    aggregated_data.forEach((row: any) => {
        // Count occurrences as source
        source_degrees.set(row.source, (source_degrees.get(row.source) || 0) + 1);
        // Count occurrences as target
        target_degrees.set(row.target, (target_degrees.get(row.target) || 0) + 1);
    });

    // Python: # Add degree information to each row
    const final_data = aggregated_data.map((row: any) => ({
        ...row,
        source_degree: source_degrees.get(row.source) || 0,
        target_degree: target_degrees.get(row.target) || 0,
        combined_degree: (source_degrees.get(row.source) || 0) + (target_degrees.get(row.target) || 0)
    }));

    // Filter to final columns
    const finalColumns = RELATIONSHIPS_FINAL_COLUMNS;
    const filteredData = final_data.map((row: any) => {
        const newRow: Record<string, any> = {};
        finalColumns.forEach(col => {
            if (col in row) {
                newRow[col] = row[col];
            }
        });
        return newRow;
    });

    return {
        columns: finalColumns,
        data: filteredData
    };
}

// Compatibility export for existing code
export const updateAndMergeRelationships = _group_and_resolve_relationships;
﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A package containing all built-in workflow definitions.
 */

import { PipelineFactory } from './factory';

// Import all workflow functions
import { runWorkflow as runCreateBaseTextUnits } from './create_base_text_units';
import { runWorkflow as runCreateCommunities } from './create_communities';
import { runWorkflow as runCreateCommunityReports } from './create_community_reports';
import { runWorkflow as runCreateCommunityReportsText } from './create_community_reports_text';
import { runWorkflow as runCreateFinalDocuments } from './create_final_documents';
import { runWorkflow as runCreateFinalTextUnits } from './create_final_text_units';
import { runWorkflow as runExtractCovariates } from './extract_covariates';
import { runWorkflow as runExtractGraph } from './extract_graph';
import { runWorkflow as runExtractGraphNlp } from './extract_graph_nlp';
import { runWorkflow as runFinalizeGraph } from './finalize_graph';
import { runWorkflow as runGenerateTextEmbeddings } from './generate_text_embeddings';
import { runWorkflow as runLoadInputDocuments } from './load_input_documents';
import { runWorkflow as runLoadUpdateDocuments } from './load_update_documents';
import { runWorkflow as runPruneGraph } from './prune_graph';
import { runWorkflow as runUpdateCleanState } from './update_clean_state';
import { runWorkflow as runUpdateCommunities } from './update_communities';
import { runWorkflow as runUpdateCommunityReports } from './update_community_reports';
import { runWorkflow as runUpdateCovariates } from './update_covariates';
import { runWorkflow as runUpdateEntitiesRelationships } from './update_entities_relationships';
import { runWorkflow as runUpdateFinalDocuments } from './update_final_documents';
import { runWorkflow as runUpdateTextEmbeddings } from './update_text_embeddings';
import { runWorkflow as runUpdateTextUnits } from './update_text_units';

// Register all built-in workflows at once
PipelineFactory.registerAll({
    "load_input_documents": runLoadInputDocuments,
    "load_update_documents": runLoadUpdateDocuments,
    "create_base_text_units": runCreateBaseTextUnits,
    "create_communities": runCreateCommunities,
    "create_community_reports_text": runCreateCommunityReportsText,
    "create_community_reports": runCreateCommunityReports,
    "extract_covariates": runExtractCovariates,
    "create_final_documents": runCreateFinalDocuments,
    "create_final_text_units": runCreateFinalTextUnits,
    "extract_graph_nlp": runExtractGraphNlp,
    "extract_graph": runExtractGraph,
    "finalize_graph": runFinalizeGraph,
    "generate_text_embeddings": runGenerateTextEmbeddings,
    "prune_graph": runPruneGraph,
    "update_final_documents": runUpdateFinalDocuments,
    "update_text_embeddings": runUpdateTextEmbeddings,
    "update_community_reports": runUpdateCommunityReports,
    "update_entities_relationships": runUpdateEntitiesRelationships,
    "update_communities": runUpdateCommunities,
    "update_covariates": runUpdateCovariates,
    "update_text_units": runUpdateTextUnits,
    "update_clean_state": runUpdateCleanState,
});

// Export factory and individual workflows
export { PipelineFactory };
export * from './factory';
export * from './create_base_text_units';
export * from './create_communities';
export * from './create_community_reports';
export * from './create_community_reports_text';
export * from './create_final_documents';
export * from './create_final_text_units';
export * from './extract_covariates';
export * from './extract_graph';
export * from './extract_graph_nlp';
export * from './finalize_graph';
export * from './generate_text_embeddings';
export * from './load_input_documents';
export * from './load_update_documents';
export * from './prune_graph';
export * from './update_clean_state';
export * from './update_communities';
export * from './update_community_reports';
export * from './update_covariates';
export * from './update_entities_relationships';
export * from './update_final_documents';
export * from './update_text_embeddings';
export * from './update_text_units';

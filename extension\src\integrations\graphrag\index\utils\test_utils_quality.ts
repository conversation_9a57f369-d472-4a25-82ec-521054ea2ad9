/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * Comprehensive test file for GraphRAG index/utils module functions.
 * This file validates the functionality and quality of all TypeScript translations.
 */

import {
    // DataFrame utilities
    drop_columns,
    where_column_equals,
    antijoin,
    transform_series,
    join,
    union,
    select,
    DataFrame,
    
    // Dictionary utilities
    dict_has_keys_with_types,
    
    // Hashing utilities
    gen_sha512_hash,
    
    // Null checking utilities
    is_null,
    
    // Rate limiting utilities
    RateLimiter,
    
    // Graph utilities
    calculate_modularity,
    calculate_pmi_edge_weights,
    calculate_rrf_edge_weights,
    get_upper_threshold_by_std,
    ModularityMetric
} from './index.js';

// Mock data for testing
const mockDataFrame: DataFrame = {
    columns: ['id', 'name', 'value', 'category'],
    data: [
        { id: 1, name: 'Alice', value: 100, category: 'A' },
        { id: 2, name: 'Bob', value: 200, category: 'B' },
        { id: 3, name: '<PERSON>', value: 150, category: 'A' },
        { id: 4, name: '<PERSON>', value: 300, category: 'C' }
    ]
};

const mockNodesDF: DataFrame = {
    columns: ['title', 'frequency'],
    data: [
        { title: 'Node1', frequency: 10 },
        { title: 'Node2', frequency: 15 },
        { title: 'Node3', frequency: 8 }
    ]
};

const mockEdgesDF: DataFrame = {
    columns: ['source', 'target', 'weight'],
    data: [
        { source: 'Node1', target: 'Node2', weight: 0.8 },
        { source: 'Node2', target: 'Node3', weight: 0.6 },
        { source: 'Node1', target: 'Node3', weight: 0.4 }
    ]
};

/**
 * Test DataFrame utility functions
 */
function testDataFrameUtilities(): void {
    console.log('🧪 Testing DataFrame Utilities...');
    
    try {
        // Test drop_columns
        const dropped = drop_columns(mockDataFrame, 'category');
        console.log('✅ drop_columns executed successfully');
        console.log(`   - Original columns: ${mockDataFrame.columns.length}, After drop: ${dropped.columns.length}`);
        
        // Test where_column_equals
        const filtered = where_column_equals(mockDataFrame, 'category', 'A');
        console.log('✅ where_column_equals executed successfully');
        console.log(`   - Filtered rows: ${filtered.data.length}`);
        
        // Test antijoin
        const excludeDF: DataFrame = {
            columns: ['id'],
            data: [{ id: 1 }, { id: 3 }]
        };
        const antijoined = antijoin(mockDataFrame, excludeDF, 'id');
        console.log('✅ antijoin executed successfully');
        console.log(`   - Remaining rows: ${antijoined.data.length}`);
        
        // Test transform_series
        const transformed = transform_series(mockDataFrame, 'value', (val: number) => val * 2);
        console.log('✅ transform_series executed successfully');
        console.log(`   - First transformed value: ${transformed.data[0].value}`);
        
        // Test join
        const rightDF: DataFrame = {
            columns: ['id', 'description'],
            data: [
                { id: 1, description: 'First' },
                { id: 2, description: 'Second' }
            ]
        };
        const joined = join(mockDataFrame, rightDF, 'id', 'left');
        console.log('✅ join executed successfully');
        console.log(`   - Joined columns: ${joined.columns.length}`);
        
        // Test union
        const unioned = union(mockDataFrame, rightDF);
        console.log('✅ union executed successfully');
        console.log(`   - Union rows: ${unioned.data.length}`);
        
        // Test select
        const selected = select(mockDataFrame, 'id', 'name');
        console.log('✅ select executed successfully');
        console.log(`   - Selected columns: ${selected.columns.length}`);
        
    } catch (error) {
        console.error('❌ DataFrame utilities test failed:', error);
        throw error;
    }
}

/**
 * Test dictionary utility functions
 */
function testDictionaryUtilities(): void {
    console.log('🧪 Testing Dictionary Utilities...');
    
    try {
        const testDict = { name: 'test', age: '25', active: 'true' };
        const expectedFields: Array<[string, new (value: any) => any]> = [
            ['name', String],
            ['age', Number],
            ['active', Boolean]
        ];
        
        const result = dict_has_keys_with_types(testDict, expectedFields, true);
        console.log('✅ dict_has_keys_with_types executed successfully');
        console.log(`   - Validation result: ${result}`);
        console.log(`   - Converted age: ${testDict.age} (type: ${typeof testDict.age})`);
        
    } catch (error) {
        console.error('❌ Dictionary utilities test failed:', error);
        throw error;
    }
}

/**
 * Test hashing utility functions
 */
function testHashingUtilities(): void {
    console.log('🧪 Testing Hashing Utilities...');
    
    try {
        const testItem = { id: 1, name: 'test', value: 100 };
        const hashcode = ['id', 'name'];
        
        const hash = gen_sha512_hash(testItem, hashcode);
        console.log('✅ gen_sha512_hash executed successfully');
        console.log(`   - Hash length: ${hash.length}`);
        console.log(`   - Hash sample: ${hash.substring(0, 16)}...`);
        
        // Test consistency
        const hash2 = gen_sha512_hash(testItem, hashcode);
        const isConsistent = hash === hash2;
        console.log(`   - Hash consistency: ${isConsistent}`);
        
    } catch (error) {
        console.error('❌ Hashing utilities test failed:', error);
        throw error;
    }
}

/**
 * Test null checking utility functions
 */
function testNullUtilities(): void {
    console.log('🧪 Testing Null Utilities...');
    
    try {
        const testCases = [
            { value: null, expected: true },
            { value: undefined, expected: true },
            { value: NaN, expected: true },
            { value: 0, expected: false },
            { value: '', expected: false },
            { value: false, expected: false },
            { value: 'test', expected: false }
        ];
        
        let passed = 0;
        for (const testCase of testCases) {
            const result = is_null(testCase.value);
            if (result === testCase.expected) {
                passed++;
            } else {
                console.warn(`   ⚠️  Unexpected result for ${testCase.value}: got ${result}, expected ${testCase.expected}`);
            }
        }
        
        console.log('✅ is_null executed successfully');
        console.log(`   - Test cases passed: ${passed}/${testCases.length}`);
        
    } catch (error) {
        console.error('❌ Null utilities test failed:', error);
        throw error;
    }
}

/**
 * Test rate limiting utility functions
 */
async function testRateLimiterUtilities(): Promise<void> {
    console.log('🧪 Testing Rate Limiter Utilities...');
    
    try {
        const rateLimiter = new RateLimiter(2, 1); // 2 requests per second
        
        const startTime = performance.now();
        
        // Make 3 requests (should be rate limited)
        await rateLimiter.acquire();
        await rateLimiter.acquire();
        await rateLimiter.acquire();
        
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        console.log('✅ RateLimiter executed successfully');
        console.log(`   - Duration for 3 requests: ${duration.toFixed(2)}ms`);
        console.log(`   - Rate limiting working: ${duration > 500 ? 'Yes' : 'No'}`);
        
    } catch (error) {
        console.error('❌ Rate limiter utilities test failed:', error);
        throw error;
    }
}

/**
 * Test graph utility functions
 */
function testGraphUtilities(): void {
    console.log('🧪 Testing Graph Utilities...');
    
    try {
        // Test PMI edge weights calculation
        const pmiResult = calculate_pmi_edge_weights(mockNodesDF, mockEdgesDF);
        console.log('✅ calculate_pmi_edge_weights executed successfully');
        console.log(`   - PMI result rows: ${pmiResult.data.length}`);
        
        // Test RRF edge weights calculation
        const rrfResult = calculate_rrf_edge_weights(mockNodesDF, mockEdgesDF);
        console.log('✅ calculate_rrf_edge_weights executed successfully');
        console.log(`   - RRF result rows: ${rrfResult.data.length}`);
        
        // Test upper threshold calculation
        const testData = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
        const threshold = get_upper_threshold_by_std(testData, 1.5);
        console.log('✅ get_upper_threshold_by_std executed successfully');
        console.log(`   - Threshold value: ${threshold.toFixed(2)}`);
        
    } catch (error) {
        console.error('❌ Graph utilities test failed:', error);
        throw error;
    }
}

/**
 * Run all utility tests
 */
export async function runUtilsQualityTests(): Promise<void> {
    console.log('🚀 Starting GraphRAG Index/Utils Module Quality Tests...\n');
    
    try {
        testDataFrameUtilities();
        console.log('');
        
        testDictionaryUtilities();
        console.log('');
        
        testHashingUtilities();
        console.log('');
        
        testNullUtilities();
        console.log('');
        
        await testRateLimiterUtilities();
        console.log('');
        
        testGraphUtilities();
        console.log('');
        
        console.log('🎉 All GraphRAG Index/Utils Module Quality Tests Passed!');
        
    } catch (error) {
        console.error('💥 GraphRAG Index/Utils Module Quality Tests Failed:', error);
        throw error;
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    runUtilsQualityTests().catch(console.error);
}

/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing the explode_communities method definition.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

import { DataFrame } from '../../../data_model/types.js';
import * as schemas from '../../../data_model/schemas.js';

/**
 * Explode a list of communities into nodes for filtering.
 * Matches the Python explode_communities function exactly.
 */
export function explode_communities(
    communities: DataFrame,
    entities: DataFrame
): DataFrame {
    // Python: community_join = communities.explode("entity_ids").loc[:, ["community", "level", "entity_ids"]]
    const exploded_communities_data: Record<string, any>[] = [];

    communities.data.forEach(row => {
        const entity_ids = row["entity_ids"];
        if (Array.isArray(entity_ids)) {
            entity_ids.forEach(entity_id => {
                exploded_communities_data.push({
                    community: row["community"],
                    level: row["level"],
                    entity_ids: entity_id
                });
            });
        } else {
            exploded_communities_data.push({
                community: row["community"],
                level: row["level"],
                entity_ids: entity_ids
            });
        }
    });

    const community_join: DataFrame = {
        columns: ["community", "level", "entity_ids"],
        data: exploded_communities_data
    };

    // Python: nodes = entities.merge(community_join, left_on="id", right_on="entity_ids", how="left")
    const nodes_data: Record<string, any>[] = [];

    entities.data.forEach(entity_row => {
        const matches = community_join.data.filter(comm_row =>
            entity_row["id"] === comm_row["entity_ids"]
        );

        if (matches.length > 0) {
            matches.forEach(match => {
                nodes_data.push({
                    ...entity_row,
                    community: match["community"],
                    level: match["level"],
                    entity_ids: match["entity_ids"]
                });
            });
        } else {
            // Left join - include entity even if no community match
            nodes_data.push({
                ...entity_row,
                community: null,
                level: null,
                entity_ids: null
            });
        }
    });

    // Combine all columns
    const all_columns = new Set([...entities.columns, ...community_join.columns]);
    const nodes: DataFrame = {
        columns: Array.from(all_columns),
        data: nodes_data
    };

    // Python: return nodes.loc[nodes.loc[:, COMMUNITY_ID] != -1]
    const filtered_data = nodes.data.filter(row =>
        row[schemas.COMMUNITY_ID] !== -1 &&
        row[schemas.COMMUNITY_ID] !== null &&
        row[schemas.COMMUNITY_ID] !== undefined
    );

    return {
        columns: nodes.columns,
        data: filtered_data
    };
}

/**
 * Legacy function for backward compatibility.
 * Explode communities into separate rows (single parameter version).
 */
export function explode_communities_legacy(df: DataFrame): DataFrame {
    // Python: df = df.copy()
    const df_copy = {
        columns: [...df.columns],
        data: df.data.map(row => ({ ...row }))
    };

    // Python: df[schemas.COMMUNITY_ID] = df[schemas.COMMUNITY_ID].astype(str)
    df_copy.data.forEach(row => {
        if (row[schemas.COMMUNITY_ID] !== null && row[schemas.COMMUNITY_ID] !== undefined) {
            row[schemas.COMMUNITY_ID] = String(row[schemas.COMMUNITY_ID]);
        }
    });

    // Python: df = df.explode(schemas.COMMUNITY_ID)
    const exploded_data: Record<string, any>[] = [];

    df_copy.data.forEach(row => {
        const community_ids = row[schemas.COMMUNITY_ID];

        if (Array.isArray(community_ids)) {
            // If COMMUNITY_ID is an array, explode it
            community_ids.forEach(community_id => {
                exploded_data.push({
                    ...row,
                    [schemas.COMMUNITY_ID]: community_id
                });
            });
        } else if (typeof community_ids === 'string' && community_ids.includes(',')) {
            // If COMMUNITY_ID is a comma-separated string, split and explode
            const ids = community_ids.split(',').map(id => id.trim()).filter(id => id.length > 0);
            ids.forEach(community_id => {
                exploded_data.push({
                    ...row,
                    [schemas.COMMUNITY_ID]: community_id
                });
            });
        } else {
            // Single value, keep as is
            exploded_data.push(row);
        }
    });

    // Python: df[schemas.COMMUNITY_ID] = df[schemas.COMMUNITY_ID].astype(int)
    exploded_data.forEach(row => {
        if (row[schemas.COMMUNITY_ID] !== null && row[schemas.COMMUNITY_ID] !== undefined) {
            const community_id = String(row[schemas.COMMUNITY_ID]).trim();
            if (community_id !== '' && !isNaN(Number(community_id))) {
                row[schemas.COMMUNITY_ID] = parseInt(community_id, 10);
            }
        }
    });

    // Python: return df.reset_index(drop=True)
    return {
        columns: df_copy.columns,
        data: exploded_data
    };
}

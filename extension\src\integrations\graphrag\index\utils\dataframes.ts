﻿/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing DataFrame utilities.
 * Converted from Python to TypeScript for GraphRAG integration.
 */

// DataFrame interface definition
export interface DataFrame {
    columns: string[];
    data: Record<string, any>[];
}

// Type definitions matching Python pandas types
export type MergeHow = 'left' | 'right' | 'inner' | 'outer';

/**
 * Drop columns from a dataframe.
 * Python: def drop_columns(df: pd.DataFrame, *column: str) -> pd.DataFrame
 */
export function drop_columns(df: DataFrame, ...column: string[]): DataFrame {
    // Python: return df.drop(list(column), axis=1)
    const new_columns = df.columns.filter(col => !column.includes(col));
    const new_data = df.data.map(row => {
        const new_row: Record<string, any> = {};
        new_columns.forEach(col => {
            if (col in row) {
                new_row[col] = row[col];
            }
        });
        return new_row;
    });

    return {
        columns: new_columns,
        data: new_data
    };
}

/**
 * Return a filtered DataFrame where a column equals a value.
 * Python: def where_column_equals(df: pd.DataFrame, column: str, value: Any) -> pd.DataFrame
 */
export function where_column_equals(df: DataFrame, column: string, value: any): DataFrame {
    // Python: return cast("pd.DataFrame", df[df[column] == value])
    const filtered_data = df.data.filter(row => row[column] === value);
    return {
        columns: df.columns,
        data: filtered_data
    };
}

/**
 * Return an anti-joined dataframe.
 * Python: def antijoin(df: pd.DataFrame, exclude: pd.DataFrame, column: str) -> pd.DataFrame
 */
export function antijoin(df: DataFrame, exclude: DataFrame, column: string): DataFrame {
    // Python: return df.loc[~df.loc[:, column].isin(exclude.loc[:, column])]
    const exclude_values = new Set(exclude.data.map(row => row[column]));
    const filtered_data = df.data.filter(row => !exclude_values.has(row[column]));

    return {
        columns: df.columns,
        data: filtered_data
    };
}

/**
 * Apply a transformation function to a series.
 * Python: def transform_series(series: pd.Series, fn: Callable[[Any], Any]) -> pd.Series
 * Note: This TypeScript version works on DataFrame columns instead of standalone Series
 */
export function transform_series(df: DataFrame, column: string, fn: (value: any) => any): DataFrame {
    // Python: return cast("pd.Series", series.apply(fn))
    const new_data = df.data.map(row => ({
        ...row,
        [column]: fn(row[column])
    }));

    return {
        columns: df.columns,
        data: new_data
    };
}

/**
 * Perform a table join.
 * Python: def join(left: pd.DataFrame, right: pd.DataFrame, key: str, strategy: MergeHow = "left") -> pd.DataFrame
 */
export function join(
    left: DataFrame,
    right: DataFrame,
    key: string,
    strategy: MergeHow = 'left'
): DataFrame {
    // Python: return left.merge(right, on=key, how=strategy)
    const right_map = new Map<any, Record<string, any>>();
    right.data.forEach(row => {
        right_map.set(row[key], row);
    });

    const all_columns = new Set([...left.columns, ...right.columns]);
    const joined_data: Record<string, any>[] = [];

    switch (strategy) {
        case 'left':
            left.data.forEach(left_row => {
                const right_row = right_map.get(left_row[key]) || {};
                joined_data.push({ ...left_row, ...right_row });
            });
            break;
        case 'inner':
            left.data.forEach(left_row => {
                const right_row = right_map.get(left_row[key]);
                if (right_row) {
                    joined_data.push({ ...left_row, ...right_row });
                }
            });
            break;
        case 'right':
            right.data.forEach(right_row => {
                const left_row = left.data.find(lr => lr[key] === right_row[key]) || {};
                joined_data.push({ ...left_row, ...right_row });
            });
            break;
        case 'outer':
            // Full outer join implementation
            const all_keys = new Set([
                ...left.data.map(row => row[key]),
                ...right.data.map(row => row[key])
            ]);
            all_keys.forEach(k => {
                const left_row = left.data.find(row => row[key] === k) || {};
                const right_row = right_map.get(k) || {};
                joined_data.push({ ...left_row, ...right_row });
            });
            break;
        default:
            throw new Error(`Join strategy ${strategy} not implemented`);
    }

    return {
        columns: Array.from(all_columns),
        data: joined_data
    };
}

/**
 * Perform a union operation on the given set of dataframes.
 * Python: def union(*frames: pd.DataFrame) -> pd.DataFrame
 */
export function union(...frames: DataFrame[]): DataFrame {
    // Python: return pd.concat(list(frames))
    const all_columns = new Set<string>();
    const all_data: Record<string, any>[] = [];

    frames.forEach(df => {
        df.columns.forEach(col => all_columns.add(col));
        all_data.push(...df.data);
    });

    return {
        columns: Array.from(all_columns),
        data: all_data
    };
}

/**
 * Select columns from a dataframe.
 * Python: def select(df: pd.DataFrame, *columns: str) -> pd.DataFrame
 */
export function select(df: DataFrame, ...columns: string[]): DataFrame {
    // Python: return cast("pd.DataFrame", df[list(columns)])
    const new_data = df.data.map(row => {
        const new_row: Record<string, any> = {};
        columns.forEach(col => {
            if (col in row) {
                new_row[col] = row[col];
            }
        });
        return new_row;
    });

    return {
        columns: columns,
        data: new_data
    };
}

// Compatibility exports for existing code
export const dropColumns = drop_columns;
export const whereColumnEquals = where_column_equals;
export const transformSeries = transform_series;

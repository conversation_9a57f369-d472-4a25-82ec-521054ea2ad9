/**
 * Copyright (c) 2024 Microsoft Corporation.
 * Licensed under the MIT License
 */

/**
 * A module containing runWorkflow method definition.
 */

import { DataFrame } from '../../data_model/types';
import { PipelineCache } from '../../cache/pipeline_cache';
import { WorkflowCallbacks } from '../../callbacks/workflow_callbacks';
import { graphragConfigDefaults } from '../../config/defaults';
import { AsyncType } from '../../config/enums';
import { GraphRagConfig } from '../../config/models/graph_rag_config';
import { finalizeCommunityReports } from '../operations/finalize_community_reports';
import { explodeCommunities } from '../operations/summarize_communities/explode_communities';
import { 
    buildLevelContext, 
    buildLocalContext 
} from '../operations/summarize_communities/text_unit_context/context_builder';
import { summarizeCommunities } from '../operations/summarize_communities/summarize_communities';
import { PipelineRunContext } from '../typing/context';
import { WorkflowFunctionOutput } from '../typing/workflow';
import { 
    loadTableFromStorage, 
    writeTableToStorage 
} from '../../utils/storage';

const logger = console;

/**
 * All the steps to transform community reports using text units.
 */
export async function runWorkflow(
    config: GraphRagConfig,
    context: PipelineRunContext,
): Promise<WorkflowFunctionOutput> {
    logger.info("Workflow started: create_community_reports_text");
    
    const entities = await loadTableFromStorage("entities", context.outputStorage);
    const communities = await loadTableFromStorage("communities", context.outputStorage);
    const textUnits = await loadTableFromStorage("text_units", context.outputStorage);

    const communityReportsLlmSettings = config.getLanguageModelConfig(
        config.communityReports.modelId
    );
    const asyncMode = communityReportsLlmSettings.asyncMode;
    const numThreads = communityReportsLlmSettings.concurrentRequests;
    const summarizationStrategy = config.communityReports.resolvedStrategy(
        config.rootDir, 
        communityReportsLlmSettings
    );

    const output = await createCommunityReportsText(
        entities,
        communities,
        textUnits,
        context.callbacks,
        context.cache,
        summarizationStrategy,
        asyncMode,
        numThreads,
    );

    await writeTableToStorage(output, "community_reports", context.outputStorage);

    logger.info("Workflow completed: create_community_reports_text");
    return { result: output };
}

export interface CreateCommunityReportsTextParams {
    entities: DataFrame;
    communities: DataFrame;
    textUnits: DataFrame;
    callbacks: WorkflowCallbacks;
    cache: PipelineCache;
    summarizationStrategy: Record<string, any>;
    asyncMode?: AsyncType;
    numThreads?: number;
}

/**
 * All the steps to transform community reports using text units.
 */
export async function createCommunityReportsText(
    entities: DataFrame,
    communities: DataFrame,
    textUnits: DataFrame,
    callbacks: WorkflowCallbacks,
    cache: PipelineCache,
    summarizationStrategy: Record<string, any>,
    asyncMode: AsyncType = AsyncType.AsyncIO,
    numThreads: number = 4,
): Promise<DataFrame> {
    // Explode communities into nodes
    const nodes = explodeCommunities(communities, entities);

    // Set extraction prompt from text prompt
    summarizationStrategy["extraction_prompt"] = summarizationStrategy["text_prompt"];

    // Get max input length from strategy or defaults
    const maxInputLength = summarizationStrategy["max_input_length"] || 
        graphragConfigDefaults.communityReports.maxInputLength;

    // Build local contexts using text units
    const localContexts = buildLocalContext(
        communities,
        textUnits,
        nodes,
        maxInputLength,
    );

    // Summarize communities
    const communityReports = await summarizeCommunities(
        nodes,
        communities,
        localContexts,
        buildLevelContext,
        callbacks,
        cache,
        summarizationStrategy,
        maxInputLength,
        asyncMode,
        numThreads,
    );

    return finalizeCommunityReports(communityReports, communities);
}
